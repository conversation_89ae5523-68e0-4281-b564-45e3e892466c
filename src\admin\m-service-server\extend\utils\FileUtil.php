<?php
namespace utils;

/**
 * 文件管理工具类
 * 
 * 用于管理本地存储文件的删除等操作
 * 支持不同环境：
 * - 开发环境（Windows）：D:\upload
 * - 生产环境（CentOS）：/home/<USER>
 */
class FileUtil
{
    /**
     * 获取文件的物理路径
     * 
     * @param string $url 文件URL，如 http://localhost/pics/20250629/image/1bea6677627d1405efe99d077a4d09aa.jpg
     * @return string 物理文件路径
     */
    public static function getPhysicalPath(string $url): string
    {
        // 如果URL为空，直接返回空
        if (empty($url)) {
            return '';
        }

        // 提取URL中的相对路径部分
        $baseUrl = self::isWindows() ? 'http://localhost/pics/' : 'https://your-domain.com/files/';
        $relativePath = str_replace($baseUrl, '', $url);

        // 获取基础路径
        $basePath = self::getBasePath();

        // 处理斜杠，确保与操作系统兼容
        if (self::isWindows()) {
            $relativePath = str_replace('/', '\\', $relativePath);
        } else {
            $relativePath = str_replace('\\', '/', $relativePath);
        }

        // 组合完整的物理路径
        return $basePath . $relativePath;
    }

    /**
     * 根据环境获取基础存储路径
     * 
     * @return string 存储基础路径
     */
    private static function getBasePath(): string
    {
        return self::isWindows() ? 'D:\\upload\\' : '/home/<USER>/';
    }

    /**
     * 判断当前是否为Windows环境
     * 
     * @return bool
     */
    private static function isWindows(): bool
    {
        return strtoupper(substr(PHP_OS, 0, 3)) === 'WIN';
    }

    /**
     * 删除文件
     * 
     * @param string $url 文件URL或物理路径
     * @return bool 是否删除成功
     */
    public static function deleteFile(string $url): bool
    {
        // 如果URL为空，视为删除成功
        if (empty($url)) {
            return true;
        }

        try {
            // 判断是否为URL，如果是则转换为物理路径
            $physicalPath = strpos($url, 'http') === 0 ? self::getPhysicalPath($url) : $url;

            // 检查文件是否存在，不存在则视为删除成功
            if (!file_exists($physicalPath)) {
                return true;
            }

            // 删除文件
            return unlink($physicalPath);
        } catch (\Exception $e) {
            // 记录错误日志
            if (class_exists('\\app\\api\\services\\LogService')) {
                \app\api\services\LogService::error($e, ['msg' => "删除文件失败：{$url}"]);
            }
            return false;
        }
    }

    /**
     * 批量删除文件
     * 
     * @param array $urls URL数组
     * @return array 成功和失败的文件列表
     */
    public static function batchDeleteFiles(array $urls): array
    {
        $result = [
            'success' => [],
            'failed' => []
        ];

        foreach ($urls as $url) {
            if (self::deleteFile($url)) {
                $result['success'][] = $url;
            } else {
                $result['failed'][] = $url;
            }
        }

        return $result;
    }

    /**
     * 获取文件扩展名
     * 
     * @param string $filePath 文件路径
     * @return string 文件扩展名（小写）
     */
    public static function getFileExtension(string $filePath): string
    {
        return strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
    }

    /**
     * 检查目录是否存在，不存在则创建
     * 
     * @param string $dirPath 目录路径
     * @return bool 是否存在或创建成功
     */
    public static function ensureDirectoryExists(string $dirPath): bool
    {
        if (empty($dirPath)) {
            return false;
        }

        if (!is_dir($dirPath)) {
            return mkdir($dirPath, 0777, true);
        }

        return true;
    }

    /**
     * 判断文件是否为图片
     * 
     * @param string $filePath 文件路径
     * @return bool 是否为图片
     */
    public static function isImage(string $filePath): bool
    {
        $extension = self::getFileExtension($filePath);
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];

        return in_array($extension, $imageExtensions);
    }

    /**
     * 生成文件的URL路径
     * 
     * @param string $relativePath 相对路径
     * @return string URL路径
     */
    public static function generateUrl(string $relativePath): string
    {
        // 确保相对路径使用正斜杠
        $relativePath = str_replace('\\', '/', $relativePath);

        // 移除开头的斜杠
        if (strpos($relativePath, '/') === 0) {
            $relativePath = substr($relativePath, 1);
        }

        $baseUrl = self::isWindows() ? 'http://localhost/pics/' : 'https://your-domain.com/files/';
        return $baseUrl . $relativePath;
    }
}